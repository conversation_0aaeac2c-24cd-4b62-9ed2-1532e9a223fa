/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header and Navigation */
header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h1 {
    color: #2c3e50;
    font-size: 2rem;
    font-weight: bold;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: #3498db;
}

/* Hero Section */
.hero {
    display: flex;
    align-items: center;
    min-height: 100vh;
    padding: 100px 20px 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.hero-content {
    flex: 1;
    max-width: 600px;
    margin-right: 2rem;
}

.hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    font-weight: bold;
}

.hero-content p {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}



.hero-image {
    flex: 1;
    max-width: 500px;
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

/* About Section */
.about {
    padding: 80px 0;
    background: #f8f9fa;
}

.about h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.about-content {
    display: flex;
    align-items: center;
    gap: 3rem;
}

.about-text {
    flex: 1;
}

.about-text p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    line-height: 1.8;
}

.about-image {
    flex: 1;
    max-width: 500px;
}

.about-image img {
    width: 100%;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

/* Services Section */
.services {
    padding: 80px 0;
    background: white;
}

.services h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.service-card {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.service-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 1.5rem;
}

.service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.service-card p {
    line-height: 1.6;
    color: #666;
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background: #2c3e50;
    color: white;
}

.contact h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
}

.contact-content {
    display: flex;
    align-items: center;
    gap: 3rem;
}

.contact-info {
    flex: 1;
}

.contact-info h3 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
}

.contact-info p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.contact-details p {
    margin-bottom: 0.8rem;
}

.contact-image {
    flex: 1;
    max-width: 400px;
}

.contact-image img {
    width: 100%;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.3);
}

/* Footer */
footer {
    background: #1a252f;
    color: white;
    text-align: center;
    padding: 2rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero {
        flex-direction: column;
        text-align: center;
        padding: 120px 20px 50px;
    }
    
    .hero-content {
        margin-right: 0;
        margin-bottom: 2rem;
    }
    
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .about-content,
    .contact-content {
        flex-direction: column;
    }
    
    .nav-menu {
        gap: 1rem;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
    }
}
